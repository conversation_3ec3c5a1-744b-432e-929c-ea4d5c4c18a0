# 🏦 سیستم حسابداری صرافی - مستندات فنی

## 📋 فهرست مطالب

- [معماری سیستم](./architecture/system_architecture.md)
- [طراحی دیتابیس](./technical/database_design.md)
- [مشخصات ماژول‌ها](./technical/module_specifications.md)
- [نیازمندی‌های کسب‌وکار](./business/business_requirements.md)
- [راهنمای کاربری](./business/user_guide.md)
- [نمودارهای سیستم](./diagrams/)

## 🎯 هدف پروژه

توسعه یک نرم‌افزار حسابداری تحت وب برای مدیریت عملیات صرافی که شامل:

### ویژگی‌های کلیدی
- 👥 مدیریت مشتریان و کارمندان
- 💱 ثبت و پیگیری معاملات ارزی
- 🏢 مدیریت چندین مکان (استانبول، تبریز، تهران، دبی، چین)
- 💰 مدیریت موجودی ارز به تفکیک مکان
- 📊 گزارش‌گیری پیشرفته و صورت‌حساب
- 🔐 امنیت بالا و کنترل دسترسی
- 📱 طراحی ریسپانسیو

### ارزهای پشتیبانی شده
- دلار آمریکا (USD)
- درهم امارات (AED)
- تومان ایران (IRR)
- قابلیت افزودن ارزهای جدید

### انواع معاملات
- خرید و فروش ارز
- انتقال داخلی بین کاربران
- تحویل فیزیکی (حضوری، پیک)
- انتقال بانکی (ریال و ارز خارجی)
- معاملات نقدی
- انتقال SWIFT
- انتقال تومان (نقد و بانک)
- معاملات چند مرحله‌ای

## 🛠 تکنولوژی‌های استفاده شده

### Backend
- **Python 3.11+** - زبان برنامه‌نویسی اصلی
- **Django 4.2+** - فریمورک وب
- **Django REST Framework** - API Development
- **PostgreSQL 15+** - دیتابیس اصلی
- **Redis** - Cache و Session Management
- **Celery** - Task Queue برای پردازش‌های پس‌زمینه

### Frontend
- **HTML5** - ساختار صفحات
- **CSS3** - استایل‌دهی
- **Bootstrap 5** - UI Framework
- **jQuery** - JavaScript Library
- **Chart.js** - نمودارها و گزارش‌ها
- **DataTables** - جداول پیشرفته

### Infrastructure
- **Gunicorn** - WSGI Server
- **Nginx** - Web Server و Reverse Proxy
- **Docker** - Containerization
- **Windows Server** - سرور میزبان

### امنیت
- **AES-256** - رمزنگاری داده‌ها
- **JWT** - احراز هویت
- **HTTPS/TLS** - ارتباط امن
- **CSRF Protection** - محافظت در برابر حملات
- **SQL Injection Prevention** - محافظت دیتابیس

## 📁 ساختار پروژه

```
exchange-accounting/
├── docs/                          # مستندات پروژه
│   ├── architecture/              # معماری سیستم
│   ├── business/                  # نیازمندی‌های کسب‌وکار
│   ├── technical/                 # مستندات فنی
│   └── diagrams/                  # نمودارها
├── src/                           # کد منبع
│   ├── backend/                   # Django Backend
│   ├── frontend/                  # Static Files
│   └── config/                    # تنظیمات
├── tests/                         # تست‌ها
├── deployment/                    # فایل‌های استقرار
└── scripts/                       # اسکریپت‌های کمکی
```

## 🚀 مراحل توسعه

### فاز 1: تحلیل و طراحی (2 روز)
- تحلیل نیازمندی‌ها
- طراحی معماری سیستم
- طراحی دیتابیس
- طراحی رابط کاربری

### فاز 2: توسعه Backend (3 روز)
- پیاده‌سازی مدل‌های دیتابیس
- توسعه API های REST
- سیستم احراز هویت
- منطق کسب‌وکار

### فاز 3: توسعه Frontend (3 روز)
- طراحی رابط کاربری
- پیاده‌سازی داشبورد
- سیستم گزارش‌گیری
- بهینه‌سازی موبایل

### فاز 4: تست و استقرار (2 روز)
- تست واحد و یکپارچگی
- تست امنیتی
- استقرار روی سرور
- آموزش و تحویل

## 📞 اطلاعات تماس

**توسعه‌دهنده:** امیرحسین دادبین  
**تخصص:** Full-Stack Developer  
**تجربه:** 5+ سال در پروژه‌های مالی  
**وب‌سایت:** [daadbin.ir](https://daadbin.ir)

## 📄 مجوز

این پروژه تحت مجوز اختصاصی توسعه می‌یابد.
