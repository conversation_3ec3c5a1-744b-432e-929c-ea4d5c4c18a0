# 📋 نیازمندی‌های کسب‌وکار سیستم حسابداری صرافی

## 🎯 هدف کلی پروژه

توسعه یک نرم‌افزار حسابداری تحت وب برای مدیریت کامل عملیات صرافی که شامل مدیریت مشتریان، معاملات ارزی، موجودی، و گزارش‌گیری پیشرفته باشد.

## 👥 ذینفعان پروژه

### کاربران سیستم (دارای حساب کاربری)
- **مدیر صرافی**: مدیریت کل سیستم و نظارت بر عملیات
- **حسابدار**: ثبت معاملات و تهیه گزارش‌های مالی
- **اپراتور**: ثبت معاملات روزانه و خدمات مشتریان
- **مشاهده‌گر**: دسترسی فقط خواندنی به گزارش‌ها

### موجودیت‌های مدیریت شده (بدون حساب کاربری)
- **مشتریان حقیقی**: افراد عادی که کارمندان اطلاعاتشان را مدیریت می‌کنند
- **مشتریان حقوقی**: شرکت‌ها و سازمان‌ها که کارمندان اطلاعاتشان را مدیریت می‌کنند
- **پیک‌ها**: افراد تحویل‌دهنده که کارمندان اطلاعاتشان را ثبت و انتخاب می‌کنند

## 🏢 مکان‌های عملیاتی

سیستم باید قابلیت مدیریت چندین مکان را داشته باشد:

### مکان‌های فعلی
- **استانبول** (Istanbul)
- **تبریز** (Tabriz)
- **تهران** (Tehran)
- **دبی** (Dubai)
- **چین** (China)

### ویژگی‌های مکان
- موجودی مجزا برای هر مکان
- مدیر مسئول هر مکان
- تنظیمات خاص هر مکان
- قابلیت افزودن مکان‌های جدید

## 💱 ارزهای پشتیبانی شده

### ارزهای اصلی
- **دلار آمریکا (USD)**: ارز اصلی معاملات
- **درهم امارات (AED)**: ارز منطقه خلیج فارس
- **تومان ایران (IRR)**: ارز محلی

### ویژگی‌های ارز
- نرخ خرید و فروش جداگانه
- نرخ‌های مختلف برای هر مکان
- تاریخچه تغییرات نرخ
- قابلیت افزودن ارزهای جدید

## 👤 مدیریت مشتریان

### اطلاعات مشتری
- **اطلاعات شخصی**: نام، نام خانوادگی، شماره تلفن
- **اطلاعات تماس**: ایمیل (اختیاری)
- **اطلاعات کسب‌وکار**: نام شرکت، توضیحات
- **یادداشت‌ها**: یادداشت‌های داخلی
- **گروه واتساپ**: شناسه گروه اختصاصی (7-8 نفر تیم + مشتری)

### موجودی مشتری
- موجودی به تفکیک ارز
- تاریخچه تغییرات موجودی
- امکان موجودی منفی (بدهی)
- محاسبه خودکار موجودی

## 💰 انواع معاملات

### 1. خرید ارز از مشتری
- دریافت ارز خارجی از مشتری
- پرداخت معادل تومان
- ثبت نرخ خرید
- محاسبه کمیسیون

### 2. فروش ارز به مشتری
- تحویل ارز خارجی به مشتری
- دریافت معادل تومان
- ثبت نرخ فروش
- محاسبه کمیسیون

### 3. انتقال داخلی
- انتقال بین کاربران سیستم
- انتقال بین مکان‌ها
- بدون کمیسیون
- ثبت علت انتقال

### 4. تحویل فیزیکی
#### تحویل حضوری
- تحویل در محل دفتر
- رسید امضا شده
- عکس رسید

#### تحویل با پیک
- انتخاب پیک از لیست
- رسید امضا شده
- عکس رسید و تحویل
- کد رهگیری

### 5. انتقال بانکی
#### انتقال ریالی
- انتقال به حساب بانکی ایرانی
- شماره پیگیری بانک
- رسید بانکی

#### انتقال ارز خارجی
- انتقال به حساب خارجی
- کد SWIFT
- مدارک بانکی

### 6. معاملات چند مرحله‌ای
- پرداخت در چندین قسط
- پیگیری هر قسط
- محاسبه موجودی تدریجی

## 📊 مدیریت موجودی

### موجودی شرکت
- موجودی به تفکیک ارز
- موجودی به تفکیک مکان
- مثال: "USD تبریز"، "USD تهران"
- به‌روزرسانی لحظه‌ای

### موجودی مشتری
- موجودی به تفکیک ارز
- امکان موجودی منفی
- تاریخچه تغییرات
- محاسبه خودکار

### کنترل موجودی
- هشدار موجودی کم
- جلوگیری از موجودی منفی (قابل تنظیم)
- گزارش موجودی لحظه‌ای

### ردیابی ترکیب معاملات
- **ترکیب موجودی**: ردیابی اینکه موجودی کل از چه معاملاتی تشکیل شده
- **مثال**: اگر 10,000 دلار موجودی داریم:
  - 3,000 دلار متعلق به علی (از معامله شماره 1001)
  - 400 دلار متعلق به احمد (از معامله شماره 1005)
  - 6,600 دلار متعلق به سایر مشتریان
- **کاربرد در گزارش‌ها**: نمایش جزئیات ترکیب موجودی در صورت‌حساب‌های شعبه
- **ردیابی چندمرحله‌ای**: پیگیری معاملاتی که در چند نوبت تکمیل می‌شوند

## 📈 گزارش‌گیری و صورت‌حساب

### صورت‌حساب مشتری
- بازه زمانی قابل انتخاب
- موجودی ارزهای مشتری
- خلاصه انتقالات ارزی در ردیف‌های تکی
- جزئیات معاملات شامل:
  - تاریخ
  - کد نوع معامله (JV, TSN, TRQ, DBN, CBS)
  - شماره معامله
  - شرح معامله
  - ارز
  - مبلغ بدهکار/بستانکار
  - موجودی پس از معامله
  - نشانگر Dr/Cr

### گزارش‌های مالی
- نرخ ارز در معاملات خرید/فروش
- نمایش جداگانه کمیسیون
- جمع ارزها + موجودی نهایی
- خروجی PDF و Excel
- لینک به رسیدها و مدارک

### فیلترهای پیشرفته
- مشتری
- ارز
- تاریخ
- مکان
- نوع معامله
- وضعیت تحویل

## 🎛️ داشبورد مدیریت

### خلاصه موجودی‌ها
- موجودی کل شرکت
- موجودی هر مکان
- موجودی مشتریان
- نمودار توزیع ارزها

### نمودارهای سود
- سود روزانه
- سود ماهانه
- مقایسه با دوره قبل
- تحلیل روند

### هشدارهای نرخ
- تغییرات نرخ ارز
- نرخ‌های غیرعادی
- هشدار معاملات بزرگ

## 👨‍💼 مدیریت کاربران و سطوح دسترسی

### نقش‌های کاربری
#### مدیر (Admin)
- دسترسی کامل به همه بخش‌ها
- مدیریت کاربران و نقش‌ها
- تنظیمات سیستم
- مشاهده همه گزارش‌ها
- مدیریت مشتریان و پیک‌ها

#### حسابدار (Accountant)
- ثبت و ویرایش معاملات
- تهیه گزارش‌های مالی
- مدیریت موجودی
- تنظیم نرخ ارز
- مدیریت اطلاعات مشتریان

#### اپراتور (Operator)
- ثبت معاملات روزانه
- مشاهده اطلاعات مشتریان
- ثبت تحویل‌ها (حضوری و پیک)
- دسترسی محدود به گزارش‌ها
- انتخاب پیک برای تحویل

#### مشاهده‌گر (Viewer)
- مشاهده فقط خواندنی
- دسترسی به گزارش‌ها
- عدم امکان ویرایش

### کنترل دسترسی
- مجوزهای قابل تنظیم توسط مدیر
- محدودیت دسترسی بر اساس مکان
- لاگ کامل فعالیت‌های کاربران

## 🔔 سیستم اعلان‌ها

### اعلان‌های واتساپ
- پیش‌نمایش پیام
- ارسال دستی
- یکپارچگی با گروه‌های مشتریان
- اعلان دسکتاپ

### هشدارهای سیستمی
- تغییر نرخ ارز
- معاملات بزرگ
- موجودی کم
- خطاهای سیستم

## 🔒 امنیت و پشتیبان‌گیری

### امنیت
- رمزنگاری AES-256 برای داده‌های حساس
- لاگ کامل تمام عملیات
- کنترل دسترسی چندسطحه
- احراز هویت قوی

### پشتیبان‌گیری
- پشتیبان‌گیری خودکار روزانه
- ذخیره در مکان امن
- تست دوره‌ای بازیابی
- رمزنگاری پشتیبان‌ها

## 📱 سازگاری و دسترسی

### پلتفرم‌ها
- ویندوز 10 و 11
- ویندوز سرور
- مرورگرهای مدرن
- دستگاه‌های موبایل

### طراحی ریسپانسیو
- سازگار با دسکتاپ
- سازگار با تبلت
- سازگار با موبایل
- رابط کاربری بهینه

## 🌐 چندزبانه‌بودن

### پشتیبانی از زبان‌ها
- فارسی (پیش‌فرض)
- انگلیسی
- عربی (آینده)
- ترکی (آینده)

### مدیریت زبان
- فایل‌های XML برای ترجمه
- تغییر زبان بدون ریستارت
- ترجمه کامل رابط کاربری

## 🔄 مقیاس‌پذیری

### افزودن موجودیت‌های جدید
- ارزهای جدید
- مکان‌های جدید
- نوع معاملات جدید
- نقش‌های کاربری جدید

### عملکرد
- پشتیبانی از حجم بالای معاملات
- بهینه‌سازی کوئری‌های دیتابیس
- کش هوشمند
- مقیاس‌پذیری افقی
