<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="coreGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="businessGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dataGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#229954;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="utilityGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f39c12;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e67e22;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="900" fill="#f8f9fa"/>
  
  <!-- Title -->
  <rect x="0" y="0" width="1200" height="50" fill="#2c3e50"/>
  <text x="600" y="30" text-anchor="middle" fill="white" font-size="20" font-weight="bold" font-family="Arial">
    Module Interaction Diagram - Exchange Accounting System
  </text>
  
  <!-- Core Layer -->
  <rect x="50" y="80" width="1100" height="150" fill="url(#coreGrad)" stroke="#c0392b" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="600" y="105" text-anchor="middle" fill="white" font-size="16" font-weight="bold" font-family="Arial">
    Core Layer - Authentication & Security
  </text>
  
  <!-- Authentication Module -->
  <rect x="80" y="120" width="150" height="80" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="155" y="140" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Authentication</text>
  <text x="155" y="155" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Login/Logout</text>
  <text x="155" y="170" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• JWT Tokens</text>
  <text x="155" y="185" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Session Mgmt</text>
  
  <!-- User Management Module -->
  <rect x="250" y="120" width="150" height="80" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="325" y="140" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">User Management</text>
  <text x="325" y="155" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• User CRUD</text>
  <text x="325" y="170" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Role Assignment</text>
  <text x="325" y="185" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Permissions</text>
  
  <!-- Security Module -->
  <rect x="420" y="120" width="150" height="80" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="495" y="140" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Security</text>
  <text x="495" y="155" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• AES-256</text>
  <text x="495" y="170" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Access Control</text>
  <text x="495" y="185" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Audit Logging</text>
  
  <!-- Settings Module -->
  <rect x="590" y="120" width="150" height="80" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="665" y="140" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Settings</text>
  <text x="665" y="155" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• System Config</text>
  <text x="665" y="170" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Commission</text>
  <text x="665" y="185" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Preferences</text>
  
  <!-- Business Logic Layer -->
  <rect x="50" y="260" width="1100" height="200" fill="url(#businessGrad)" stroke="#2980b9" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="600" y="285" text-anchor="middle" fill="white" font-size="16" font-weight="bold" font-family="Arial">
    Business Logic Layer - Core Operations
  </text>
  
  <!-- Customer Management -->
  <rect x="80" y="300" width="140" height="80" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="150" y="320" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Customer Mgmt</text>
  <text x="150" y="335" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Customer CRUD</text>
  <text x="150" y="350" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• WhatsApp Groups</text>
  <text x="150" y="365" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Customer History</text>
  
  <!-- Location Management -->
  <rect x="240" y="300" width="140" height="80" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="310" y="320" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Location Mgmt</text>
  <text x="310" y="335" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Multi-Location</text>
  <text x="310" y="350" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Location Config</text>
  <text x="310" y="365" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Manager Assignment</text>
  
  <!-- Currency Management -->
  <rect x="400" y="300" width="140" height="80" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="470" y="320" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Currency Mgmt</text>
  <text x="470" y="335" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Exchange Rates</text>
  <text x="470" y="350" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Rate History</text>
  <text x="470" y="365" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Currency Config</text>
  
  <!-- Transaction Management -->
  <rect x="560" y="300" width="140" height="80" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="630" y="320" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Transaction Mgmt</text>
  <text x="630" y="335" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Buy/Sell</text>
  <text x="630" y="350" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Transfers</text>
  <text x="630" y="365" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Multi-stage</text>
  
  <!-- Balance Management -->
  <rect x="720" y="300" width="140" height="80" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="790" y="320" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Balance Mgmt</text>
  <text x="790" y="335" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Real-time Calc</text>
  <text x="790" y="350" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Multi-currency</text>
  <text x="790" y="365" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Balance Alerts</text>
  
  <!-- Courier Management -->
  <rect x="880" y="300" width="140" height="80" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="950" y="320" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Courier Mgmt</text>
  <text x="950" y="335" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Courier Registry</text>
  <text x="950" y="350" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Delivery Track</text>
  <text x="950" y="365" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Receipt Mgmt</text>
  
  <!-- Reporting Module -->
  <rect x="320" y="390" width="200" height="60" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="420" y="410" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Reporting Engine</text>
  <text x="420" y="425" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Statements • Financial Reports</text>
  <text x="420" y="440" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• PDF/Excel Export • Advanced Filters</text>
  
  <!-- Data Layer -->
  <rect x="50" y="490" width="1100" height="150" fill="url(#dataGrad)" stroke="#229954" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="600" y="515" text-anchor="middle" fill="white" font-size="16" font-weight="bold" font-family="Arial">
    Data Layer - Storage & Persistence
  </text>
  
  <!-- Database -->
  <rect x="200" y="530" width="180" height="80" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="290" y="550" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">PostgreSQL</text>
  <text x="290" y="565" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Primary Database</text>
  <text x="290" y="580" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• ACID Compliance</text>
  <text x="290" y="595" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Double-entry</text>
  
  <!-- Cache -->
  <rect x="400" y="530" width="180" height="80" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="490" y="550" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Redis Cache</text>
  <text x="490" y="565" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Session Store</text>
  <text x="490" y="580" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Application Cache</text>
  <text x="490" y="595" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Rate Limiting</text>
  
  <!-- File Storage -->
  <rect x="600" y="530" width="180" height="80" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="690" y="550" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">File Storage</text>
  <text x="690" y="565" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Documents</text>
  <text x="690" y="580" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Receipt Photos</text>
  <text x="690" y="595" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Backup Files</text>
  
  <!-- Utility Layer -->
  <rect x="50" y="670" width="1100" height="120" fill="url(#utilityGrad)" stroke="#e67e22" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="600" y="695" text-anchor="middle" fill="white" font-size="16" font-weight="bold" font-family="Arial">
    Utility Layer - Support Services
  </text>
  
  <!-- Notification -->
  <rect x="150" y="710" width="150" height="60" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="225" y="730" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Notifications</text>
  <text x="225" y="745" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• WhatsApp Integration</text>
  <text x="225" y="760" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• System Alerts</text>
  
  <!-- Backup -->
  <rect x="320" y="710" width="150" height="60" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="395" y="730" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Backup System</text>
  <text x="395" y="745" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Automated Backups</text>
  <text x="395" y="760" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Disaster Recovery</text>
  
  <!-- Monitoring -->
  <rect x="490" y="710" width="150" height="60" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="565" y="730" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Monitoring</text>
  <text x="565" y="745" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Performance</text>
  <text x="565" y="760" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Error Tracking</text>
  
  <!-- API Gateway -->
  <rect x="660" y="710" width="150" height="60" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="735" y="730" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">API Gateway</text>
  <text x="735" y="745" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Rate Limiting</text>
  <text x="735" y="760" text-anchor="middle" fill="white" font-size="10" font-family="Arial">• Request Routing</text>
  
  <!-- Arrows and Relationships -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
    </marker>
  </defs>
  
  <!-- Core to Business -->
  <path d="M 600 230 L 600 260" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)" fill="none"/>
  <text x="620" y="250" fill="#2c3e50" font-size="10" font-family="Arial">Auth & Security</text>
  
  <!-- Business to Data -->
  <path d="M 600 460 L 600 490" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)" fill="none"/>
  <text x="620" y="480" fill="#2c3e50" font-size="10" font-family="Arial">Data Operations</text>
  
  <!-- Business to Utility -->
  <path d="M 600 460 L 600 670" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none" stroke-dasharray="5,5"/>
  <text x="620" y="565" fill="#2c3e50" font-size="10" font-family="Arial">Support Services</text>
  
  <!-- Inter-module connections -->
  <path d="M 630 340 L 720 340" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="675" y="335" fill="#34495e" font-size="9" font-family="Arial">Updates Balance</text>
  
  <path d="M 470 380 L 470 390" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="480" y="388" fill="#34495e" font-size="9" font-family="Arial">Rate Data</text>
  
  <!-- Legend -->
  <rect x="50" y="820" width="400" height="60" fill="white" stroke="#bdc3c7" stroke-width="1" rx="5" filter="url(#shadow)"/>
  <text x="250" y="840" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold" font-family="Arial">Data Flow Legend</text>
  <path d="M 70 850 L 110 850" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)" fill="none"/>
  <text x="120" y="855" fill="#2c3e50" font-size="10" font-family="Arial">Primary Flow</text>
  <path d="M 220 850 L 260 850" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none" stroke-dasharray="5,5"/>
  <text x="270" y="855" fill="#2c3e50" font-size="10" font-family="Arial">Support Flow</text>
  <path d="M 70 865 L 110 865" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="120" y="870" fill="#2c3e50" font-size="10" font-family="Arial">Inter-module Communication</text>
</svg>
